# RabbitMQ Messaging System Demo

Bu proje, C# (.NET 9) ve Go arasında RabbitMQ kullanarak asenkron mesajlaşma sistemini gösterir.

## Sistem Akışı

1. **C# Store-Order Service** → Sipariş oluşturur ve RabbitMQ'ya gönderir
2. **Go Warehouse-Order Service** → Siparişi RabbitMQ'dan alır ve işler
3. **Go Service** → Sipariş durumunu günceller ve RabbitMQ'ya gönderir
4. **C# Service** → Durum güncellemesini RabbitMQ'dan alır

## Gereksinimler

- .NET 9 SDK
- Go 1.23+
- Docker (RabbitMQ için)

## Kurulum ve Çalıştırma

### 1. RabbitMQ'yu <PERSON>ın

```bash
docker-compose up -d
```

RabbitMQ Management UI: http://localhost:15672 (guest/guest)

### 2. Go Warehouse Service'i Başlatın

```bash
cd src/warehouse-order
go mod tidy
go run cmd/api/main.go
```

### 3. C# Store Service'i Başlatın

```bash
cd src/store-order/store-order/store-order
dotnet restore
dotnet run
```

### 4. Test Edin

Sipariş oluşturmak için:

```bash
curl -X POST http://localhost:5000/orders \
  -H "Content-Type: application/json" \
  -d '{
    "customerName": "John Doe",
    "totalAmount": 299.99
  }'
```

## Mesaj Akışı

### Order Creation (C# → Go)
- **Exchange**: `order.exchange`
- **Queue**: `order.queue`
- **Message**: `OrderCreatedDto`

### Status Update (Go → C#)
- **Exchange**: `order.status.exchange`
- **Queue**: `order.status.update.queue`
- **Message**: `OrderStatusUpdateDto`

## Proje Yapısı

```
src/
├── store-order/          # C# .NET 9 Web API
│   └── store-order/
│       ├── StoreOrder.Api/
│       ├── StoreOrder.Application/
│       ├── StoreOrder.Domain/
│       ├── StoreOrder.Infrastructure/
│       └── store-order/  # Main API project
└── warehouse-order/      # Go Service
    ├── cmd/api/          # Main application
    ├── internal/
    │   ├── config/       # Configuration
    │   ├── models/       # Data models
    │   ├── orders/       # Order processing logic
    │   └── rabbitmq/     # RabbitMQ connection
    └── pkg/              # Shared packages
```

## Teknolojiler

- **C#**: .NET 9, MassTransit, RabbitMQ
- **Go**: Go 1.23, AMQP library
- **Message Broker**: RabbitMQ
- **Containerization**: Docker
