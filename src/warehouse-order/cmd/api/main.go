package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"
	"warehouse-order/internal/config"
	"warehouse-order/internal/orders"
	"warehouse-order/internal/rabbitmq"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Connect to RabbitMQ
	conn, ch := rabbitmq.Connect(cfg.RabbitMQ.URL)
	defer conn.Close()
	defer ch.Close()

	// Setup queues and exchanges
	if err := rabbitmq.SetupQueuesAndExchanges(ch, cfg); err != nil {
		log.Fatalf("Failed to setup RabbitMQ: %s", err)
	}

	// Initialize repository and publisher
	repository := orders.NewRepository()
	publisher := orders.NewPublisher(ch, cfg.RabbitMQ.StatusExchange)

	// Initialize order service
	orderService := orders.NewService(repository, publisher)

	// Start consuming orders
	orderService.ConsumeOrders(ch, cfg.RabbitMQ.OrderQueue)

	log.Println("Warehouse Order Service started. Waiting for orders...")

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Warehouse Order Service shutting down...")
}
