package config

import (
	"os"
)

type Config struct {
	RabbitMQ RabbitMQConfig
}

type RabbitMQConfig struct {
	URL              string
	OrderQueue       string
	StatusUpdateQueue string
	OrderExchange    string
	StatusExchange   string
}

func Load() *Config {
	return &Config{
		RabbitMQ: RabbitMQConfig{
			URL:              getEnv("RABBITMQ_URL", "amqp://guest:guest@localhost:5672/"),
			OrderQueue:       getEnv("ORDER_QUEUE", "order.queue"),
			StatusUpdateQueue: getEnv("STATUS_UPDATE_QUEUE", "order.status.update.queue"),
			OrderExchange:    getEnv("ORDER_EXCHANGE", "order.exchange"),
			StatusExchange:   getEnv("STATUS_EXCHANGE", "order.status.exchange"),
		},
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
