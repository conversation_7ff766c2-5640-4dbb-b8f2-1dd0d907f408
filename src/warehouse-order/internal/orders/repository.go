package orders

import (
	"sync"
	"warehouse-order/internal/models"
)

// In-memory storage for demo purposes
type Repository struct {
	orders map[string]*models.OrderCreated
	mutex  sync.RWMutex
}

func NewRepository() *Repository {
	return &Repository{
		orders: make(map[string]*models.OrderCreated),
	}
}

func (r *Repository) SaveOrder(order *models.OrderCreated) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	r.orders[order.OrderID] = order
	return nil
}

func (r *Repository) GetOrder(orderID string) (*models.OrderCreated, bool) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	order, exists := r.orders[orderID]
	return order, exists
}

func (r *Repository) GetAllOrders() []*models.OrderCreated {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	orders := make([]*models.OrderCreated, 0, len(r.orders))
	for _, order := range r.orders {
		orders = append(orders, order)
	}
	return orders
}
