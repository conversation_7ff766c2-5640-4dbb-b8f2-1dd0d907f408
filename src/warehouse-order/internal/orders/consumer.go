package orders

import (
	"encoding/json"
	"fmt"
	"log"
	"time"
	"warehouse-order/internal/models"

	"github.com/streadway/amqp"
)

type Service struct {
	repository *Repository
	publisher  *Publisher
}

func NewService(repository *Repository, publisher *Publisher) *Service {
	return &Service{
		repository: repository,
		publisher:  publisher,
	}
}

func (s *Service) ConsumeOrders(ch *amqp.Channel, queue string) {
	msgs, err := ch.Consume(
		queue,
		"",
		false, // manual ack
		false,
		false,
		false,
		nil)

	if err != nil {
		log.Fatalf("Failed to register a consumer: %s", err)
	}

	log.Printf("Waiting for orders on queue: %s", queue)

	go func() {
		for d := range msgs {
			var order models.OrderCreated
			if err := json.Unmarshal(d.Body, &order); err != nil {
				log.Printf("Failed to unmarshal JSON: %s", err)
				d.Nack(false, false) // reject message
				continue
			}

			log.Printf("[Go Consumer] Order received: %+v", order)

			// Save order to repository
			if err := s.repository.SaveOrder(&order); err != nil {
				log.Printf("Failed to save order: %s", err)
				d.Nack(false, true) // requeue message
				continue
			}

			// Process order (simulate warehouse processing)
			s.processOrder(&order)

			// Acknowledge message
			d.Ack(false)
		}
	}()
}

func (s *Service) processOrder(order *models.OrderCreated) {
	log.Printf("[Warehouse] Processing order: %s", order.OrderID)

	// Simulate processing time
	time.Sleep(2 * time.Second)

	// Update status to Processing
	statusUpdate := models.OrderStatusUpdate{
		OrderID:   order.OrderID,
		Status:    models.OrderStatusProcessing,
		UpdatedAt: time.Now().Format(time.RFC3339),
		Message:   "Order is being processed in warehouse",
	}

	if err := s.publisher.PublishStatusUpdate(statusUpdate); err != nil {
		log.Printf("Failed to publish status update: %s", err)
		return
	}

	// Simulate more processing time
	time.Sleep(3 * time.Second)

	// Update status to Shipped
	statusUpdate = models.OrderStatusUpdate{
		OrderID:   order.OrderID,
		Status:    models.OrderStatusShipped,
		UpdatedAt: time.Now().Format(time.RFC3339),
		Message:   fmt.Sprintf("Order shipped - Total: $%.2f", order.TotalAmount),
	}

	if err := s.publisher.PublishStatusUpdate(statusUpdate); err != nil {
		log.Printf("Failed to publish status update: %s", err)
		return
	}

	log.Printf("[Warehouse] Order %s completed and shipped", order.OrderID)
}
