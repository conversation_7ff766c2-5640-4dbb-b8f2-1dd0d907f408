package orders

import (
	"encoding/json"
	"log"
	"warehouse-order/internal/models"

	"github.com/streadway/amqp"
)

type Publisher struct {
	channel        *amqp.Channel
	statusExchange string
}

func NewPublisher(channel *amqp.Channel, statusExchange string) *Publisher {
	return &Publisher{
		channel:        channel,
		statusExchange: statusExchange,
	}
}

func (p *Publisher) PublishStatusUpdate(statusUpdate models.OrderStatusUpdate) error {
	data, err := json.Marshal(statusUpdate)
	if err != nil {
		return err
	}

	err = p.channel.Publish(
		p.statusExchange,                    // exchange
		"order.status.update.queue",         // routing key
		false,                               // mandatory
		false,                               // immediate
		amqp.Publishing{
			ContentType: "application/json",
			Body:        data,
		})

	if err != nil {
		return err
	}

	log.Printf("[Go Publisher] Status update sent: OrderID=%s, Status=%s", 
		statusUpdate.OrderID, statusUpdate.Status)
	
	return nil
}
