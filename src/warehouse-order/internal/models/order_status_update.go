package models

type OrderStatus string

const (
	OrderStatusPending    OrderStatus = "Pending"
	OrderStatusProcessing OrderStatus = "Processing"
	OrderStatusShipped    OrderStatus = "Shipped"
	OrderStatusDelivered  OrderStatus = "Delivered"
	OrderStatusCancelled  OrderStatus = "Cancelled"
)

type OrderStatusUpdate struct {
	OrderID   string      `json:"order_id"`
	Status    OrderStatus `json:"status"`
	UpdatedAt string      `json:"updated_at"`
	Message   string      `json:"message,omitempty"`
}
