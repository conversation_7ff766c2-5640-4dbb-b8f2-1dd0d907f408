package rabbitmq

import (
	"encoding/json"
	"fmt"
	"log"
	"warehouse-order/internal/models"

	"github.com/streadway/amqp"
)

func ConsumeOrders(ch *amqp.Channel, queue string) {
	msgs, err := ch.Consume(
		queue,
		"",
		true,
		false,
		false,
		false,
		nil)

	if err != nil {
		log.Fatalf("Failed to register a consumer: %s", err)
	}

	go func() {
		for d := range msgs {
			var order models.OrderCreated
			if err := json.Unmarshal(d.Body, &order); err != nil {
				log.Fatalf("Failed to unmarshal JSON: %s", err)
				continue
			}
			fmt.Printf("Order created: %+v\n", order)
		}
	}()
}
