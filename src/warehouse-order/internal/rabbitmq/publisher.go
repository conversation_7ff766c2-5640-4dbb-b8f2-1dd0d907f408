package rabbitmq

import (
	"encoding/json"
	"log"

	"github.com/streadway/amqp"
)

func PublishOrder(ch *amqp.Channel, exchange string, routingKey string, body interface{}) {
	data, err := json.Marshal(body)

	if err != nil {
		log.Fatalf("Error marshalling body: %s", err)
	}

	err = ch.Publish(
		exchange,
		routingKey,
		false,
		false,
		amqp.Publishing{
			ContentType: "application/json",
			Body:        data,
		})
	if err != nil {
		log.Fatalf("Error publishing order: %s", err)
	}
	log.Printf(" [x] Sent %s", body)
}
