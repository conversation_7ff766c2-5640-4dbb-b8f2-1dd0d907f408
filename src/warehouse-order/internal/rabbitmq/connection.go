package rabbitmq

import (
	"log"
	"warehouse-order/internal/config"

	"github.com/streadway/amqp"
)

func Connect(url string) (*amqp.Connection, *amqp.Channel) {
	conn, err := amqp.Dial(url)
	if err != nil {
		log.Fatalf("Failed to connect to RabbitMQ: %s", err)
	}

	ch, err := conn.Channel()
	if err != nil {
		log.Fatalf("Failed to open a channel: %s", err)
	}

	return conn, ch
}

func SetupQueuesAndExchanges(ch *amqp.Channel, cfg *config.Config) error {
	// Declare order exchange
	err := ch.ExchangeDeclare(
		cfg.RabbitMQ.OrderExchange, // name
		"direct",                   // type
		true,                       // durable
		false,                      // auto-deleted
		false,                      // internal
		false,                      // no-wait
		nil,                        // arguments
	)
	if err != nil {
		return err
	}

	// Declare status exchange
	err = ch.ExchangeDeclare(
		cfg.RabbitMQ.StatusExchange, // name
		"direct",                    // type
		true,                        // durable
		false,                       // auto-deleted
		false,                       // internal
		false,                       // no-wait
		nil,                         // arguments
	)
	if err != nil {
		return err
	}

	// Declare order queue
	_, err = ch.QueueDeclare(
		cfg.RabbitMQ.OrderQueue, // name
		true,                    // durable
		false,                   // delete when unused
		false,                   // exclusive
		false,                   // no-wait
		nil,                     // arguments
	)
	if err != nil {
		return err
	}

	// Declare status update queue
	_, err = ch.QueueDeclare(
		cfg.RabbitMQ.StatusUpdateQueue, // name
		true,                           // durable
		false,                          // delete when unused
		false,                          // exclusive
		false,                          // no-wait
		nil,                            // arguments
	)
	if err != nil {
		return err
	}

	// Bind order queue to order exchange
	err = ch.QueueBind(
		cfg.RabbitMQ.OrderQueue,    // queue name
		cfg.RabbitMQ.OrderQueue,    // routing key
		cfg.RabbitMQ.OrderExchange, // exchange
		false,
		nil,
	)
	if err != nil {
		return err
	}

	// Bind status update queue to status exchange
	err = ch.QueueBind(
		cfg.RabbitMQ.StatusUpdateQueue, // queue name
		cfg.RabbitMQ.StatusUpdateQueue, // routing key
		cfg.RabbitMQ.StatusExchange,    // exchange
		false,
		nil,
	)
	if err != nil {
		return err
	}

	log.Println("RabbitMQ queues and exchanges setup completed")
	return nil
}
