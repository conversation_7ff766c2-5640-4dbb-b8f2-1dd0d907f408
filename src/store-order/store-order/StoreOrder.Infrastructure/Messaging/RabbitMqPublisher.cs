using MassTransit;
using Microsoft.Extensions.Logging;
using StoreOrder.Application.Common.Messaging;
using StoreOrder.Application.Orders.Events;

namespace StoreOrder.Infrastructure.Messaging;

public sealed class RabbitMqPublisher : IOrderPublisher
{
    private readonly IPublishEndpoint _publishEndpoint;
    private readonly ILogger<RabbitMqPublisher> _logger;

    public RabbitMqPublisher(IPublishEndpoint publishEndpoint, ILogger<RabbitMqPublisher> logger)
    {
        _publishEndpoint = publishEndpoint;
        _logger = logger;
    }

    public async Task PublishOrderCreatedAsync(OrderCreatedIntegrationEvent message, CancellationToken ct)
    {
        await _publishEndpoint.Publish(message, context =>
        {
            // Route to order.queue so Go service can consume
            context.SetRoutingKey("order.queue");
        });

        _logger.LogInformation("[Publisher] OrderCreated published for {OrderId}", message.OrderId);
    }
}