using MediatR;
using Microsoft.Extensions.Logging;
using StoreOrder.Application.Common.Messaging;
using StoreOrder.Application.Orders.Events;
using StoreOrder.Domain.Orders;

namespace StoreOrder.Application.Orders.Commands.CreateOrder;

public sealed class CreateOrderCommandHandler : IRequestHandler<CreateOrderCommand, Guid>
{
    private readonly ILogger<CreateOrderCommandHandler> _logger;
    private readonly IOrderPublisher _publisher;

    public CreateOrderCommandHandler(ILogger<CreateOrderCommandHandler> logger, IOrderPublisher publisher)
    {
        _logger = logger;
        _publisher = publisher;
    }

    public async Task<Guid> Handle(CreateOrderCommand request, CancellationToken cancellationToken)
    {
        var order = Order.Create(request.CustomerName, request.TotalAmount);

        _logger.LogInformation("[CreateOrder] Created order {OrderId} for {CustomerName}", order.Id, order.CustomerName);

        // Publish integration event to RabbitMQ (Go service consumes)
        await _publisher.PublishOrderCreatedAsync(new OrderCreatedIntegrationEvent(order.Id, order.CustomerName, order.TotalAmount), cancellationToken);

        return order.Id;
    }
}
