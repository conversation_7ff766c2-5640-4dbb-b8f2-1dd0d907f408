﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <Folder Include="Common\" />
      <Folder Include="Orders\Commands\CreateOrder\" />
      <Folder Include="Orders\Events\" />
      <Folder Include="Orders\Queries\" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\StoreOrder.Domain\StoreOrder.Domain.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="MediatR" Version="13.0.0" />
      <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="10.0.0-preview.6.25358.103" />
      <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="10.0.0-preview.6.25358.103" />
    </ItemGroup>

</Project>
