<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
      <Folder Include="DI\" />
      <Folder Include="Endpoints\" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\StoreOrder.Infrastructure\StoreOrder.Infrastructure.csproj" />
      <ProjectReference Include="..\StoreOrder.Application\StoreOrder.Application.csproj" />
    </ItemGroup>

    <ItemGroup>
      <None Update="appsettings.json">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="appsettings.Development.json">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>

</Project>
