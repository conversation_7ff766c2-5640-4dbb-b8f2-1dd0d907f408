﻿"restore":{"projectUniqueName":"/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Api/StoreOrder.Api.csproj","projectName":"StoreOrder.Api","projectPath":"/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Api/StoreOrder.Api.csproj","outputPath":"/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Api/obj/","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Application/StoreOrder.Application.csproj":{"projectPath":"/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Application/StoreOrder.Application.csproj"},"/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Infrastructure/StoreOrder.Infrastructure.csproj":{"projectPath":"/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Infrastructure/StoreOrder.Infrastructure.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net9.0":{"targetAlias":"net9.0","imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"/usr/local/share/dotnet/sdk/9.0.300/PortableRuntimeIdentifierGraph.json"}}