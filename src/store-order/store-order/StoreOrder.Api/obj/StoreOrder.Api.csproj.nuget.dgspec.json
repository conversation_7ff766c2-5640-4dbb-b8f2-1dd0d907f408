{"format": 1, "restore": {"/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Api/StoreOrder.Api.csproj": {}}, "projects": {"/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Api/StoreOrder.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Api/StoreOrder.Api.csproj", "projectName": "StoreOrder.Api", "projectPath": "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Api/StoreOrder.Api.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Api/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Application/StoreOrder.Application.csproj": {"projectPath": "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Application/StoreOrder.Application.csproj"}, "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Infrastructure/StoreOrder.Infrastructure.csproj": {"projectPath": "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Infrastructure/StoreOrder.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Application/StoreOrder.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Application/StoreOrder.Application.csproj", "projectName": "StoreOrder.Application", "projectPath": "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Application/StoreOrder.Application.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Application/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Domain/StoreOrder.Domain.csproj": {"projectPath": "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Domain/StoreOrder.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"MediatR": {"target": "Package", "version": "[13.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[10.0.0-preview.6.25358.103, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[10.0.0-preview.6.25358.103, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Domain/StoreOrder.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Domain/StoreOrder.Domain.csproj", "projectName": "StoreOrder.Domain", "projectPath": "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Domain/StoreOrder.Domain.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Domain/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Infrastructure/StoreOrder.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Infrastructure/StoreOrder.Infrastructure.csproj", "projectName": "StoreOrder.Infrastructure", "projectPath": "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Infrastructure/StoreOrder.Infrastructure.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Infrastructure/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Application/StoreOrder.Application.csproj": {"projectPath": "/Users/<USER>/workspace/repositories/local/interview-demo/src/store-order/store-order/StoreOrder.Application/StoreOrder.Application.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"MassTransit": {"target": "Package", "version": "[8.5.2, )"}, "MassTransit.RabbitMQ": {"target": "Package", "version": "[8.5.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}