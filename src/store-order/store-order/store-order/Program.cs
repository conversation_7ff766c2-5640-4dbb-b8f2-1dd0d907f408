using store_order.Features.Orders.CreateOrder;
using store_order.Infrastructure;

var builder = WebApplication.CreateBuilder(args);

// Add services
builder.Services.AddMassTransitWithRabbitMq(builder.Configuration);

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

// Map endpoints
app.MapCreateOrder();

app.MapGet("/", () => "Store Order API is running! Use POST /orders to create an order.");

app.Logger.LogInformation("Store Order Service started on {Environment}", app.Environment.EnvironmentName);

app.Run();