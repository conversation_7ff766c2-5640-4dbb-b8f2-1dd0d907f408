using MassTransit;
using store_order.Features.Orders.Models;

namespace store_order.Features.Orders.Consumers;

public class OrderUpdatedStatusConsumer : IConsumer<OrderStatusUpdateDto>
{
    private readonly ILogger<OrderUpdatedStatusConsumer> _logger;

    public OrderUpdatedStatusConsumer(ILogger<OrderUpdatedStatusConsumer> logger)
    {
        _logger = logger;
    }

    public Task Consume(ConsumeContext<OrderStatusUpdateDto> context)
    {
        var message = context.Message;

        _logger.LogInformation(
            "[C# Consumer] Order status updated: OrderId={OrderId}, Status={Status}, UpdatedAt={UpdatedAt}, Message={Message}",
            message.OrderId,
            message.Status,
            message.UpdatedAt,
            message.Message);

        // Burada gerçek uygulamada order'ın durumunu database'de güncellersiniz
        // örnek: await _orderRepository.UpdateOrderStatusAsync(message.OrderId, message.Status);

        return Task.CompletedTask;
    }
}