using MassTransit;

namespace store_order.Features.Orders.CreateOrder;

public static class CreateOrderEndpoint
{
    public static void MapCreateOrder(this IEndpointRouteBuilder app)
    {
        app.MapPost("/orders", async (CreateOrderCommand command, IPublishEndpoint publishEndpoint, ILogger<CreateOrderCommand> logger) =>
        {
            var orderId = Guid.NewGuid();
            var message = new OrderCreatedDto(orderId, command.CustomerName, command.TotalAmount);

            // Publish to the queue that Go service will consume
            await publishEndpoint.Publish(message, context =>
            {
                context.SetRoutingKey("order.queue");
            });

            logger.LogInformation(
                "[C# Publisher] Order created and published: OrderId={OrderId}, Customer={CustomerName}, Amount={TotalAmount}",
                orderId, command.CustomerName, command.TotalAmount);

            return Results.Ok(new {
                orderId,
                customerName = command.CustomerName,
                totalAmount = command.TotalAmount,
                status = "Order created and sent to warehouse"
            });
        });

        app.MapGet("/orders", (ILogger<CreateOrderCommand> logger) =>
        {
            logger.LogInformation("[C# API] Orders endpoint called");
            return Results.Ok(new { message = "Orders API is running. Use POST /orders to create an order." });
        });
    }
}