using MassTransit;
using store_order.Features.Orders.Consumers;

namespace store_order.Infrastructure;

public static class MassTransitConfig
{
    public static IServiceCollection AddMassTransitWithRabbitMq(this IServiceCollection services,IConfiguration config)
    {
        services.AddMassTransit(x =>
        {
            x.AddConsumer<OrderUpdatedStatusConsumer>();

            x.UsingRabbitMq((ctx, cfg) =>
            {
                cfg.Host(config["RabbitMq:Host"], "/", h =>
                {
                    h.Username(config["RabbitMq:Username"]);
                    h.Password(config["RabbitMq:Password"]);
                });

                // Order creation queue - Go service will consume from here
                cfg.Publish<store_order.Features.Orders.CreateOrder.OrderCreatedDto>(p =>
                {
                    p.ExchangeType = "direct";
                });

                // Order status update queue - C# service will consume from here
                cfg.ReceiveEndpoint("order.status.update.queue", e =>
                {
                    e.ConfigureConsumer<OrderUpdatedStatusConsumer>(ctx);
                });
            });

        });

        return services;
    }
}